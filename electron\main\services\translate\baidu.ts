import axios from "axios";
import crypto from 'crypto';
import { ITranslationRoute } from "../../interface";
import { TranslationType, VendorType } from "../../enums";
import { storeManager } from "../../modules/store";

interface BaiduTranslationResult {
    from: string;
    to: string;
    trans_result: {
        src: string;
        dst: string;
    }[];
}

interface BaiduImageTranslationResult {
    error_code: string;
    error_msg: string;
    data?: {
        from: string;
        to: string;
        content: Array<{
            src: string;
            dst: string;
            rect: string;
            lineCount: number;
            pasteImg?: string;
            points: Array<{ x: number; y: number }>;
        }>;
        sumSrc: string;
        sumDst: string;
        pasteImg?: string;
    };
}

class BaiduTranslationService {
    /**
   * 获取所有可用的百度翻译线路
   */
    async getAvailableBaiduRoutes(): Promise<ITranslationRoute[]> {
        const routes = await storeManager.getTranslateRoutes();
        return routes.filter(
            (route) =>
                route.vendor === VendorType.BAIDU && route.type === TranslationType.TEXT && route.isActive,
        );
    }

    /**
     * 获取所有可用的百度图片翻译线路
     */
    async getAvailableBaiduImageRoutes(): Promise<ITranslationRoute[]> {
        const routes = await storeManager.getTranslateRoutes();
        return routes.filter(
            (route) =>
                route.vendor === VendorType.BAIDU && route.type === TranslationType.IMAGE && route.isActive,
        );
    }

    /**
   * 生成百度翻译签名
   */
    private generateSign(appId: string, query: string, salt: string, secretKey: string): string {
        const str = appId + query + salt + secretKey;
        return crypto.createHash('md5').update(str).digest('hex');
    }

    /**
     * 转换语言代码为百度图片翻译支持的格式
     */
    private convertLanguageCodeForImage(langCode: string): string {
        const languageMap: { [key: string]: string } = {
            zh: 'zh',
            'zh-cn': 'zh',
            'zh-tw': 'cht',
            cht: 'cht',
            en: 'en',
            ja: 'jp',
            jp: 'jp',
            ko: 'kor',
            kor: 'kor',
            ms: 'may',
            may: 'may',
            th: 'th',
            ar: 'ara',
            ara: 'ara',
            vi: 'vie',
            vie: 'vie',
            hi: 'hi',
            pt: 'pt',
            fr: 'fra',
            fra: 'fra',
            de: 'de',
            it: 'it',
            es: 'spa',
            spa: 'spa',
            ru: 'ru',
            nl: 'nl',
            da: 'dan',
            dan: 'dan',
            sv: 'swe',
            swe: 'swe',
            id: 'id',
            pl: 'pl',
            ro: 'rom',
            rom: 'rom',
            tr: 'tr',
            el: 'el',
            hu: 'hu',
        };

        return languageMap[langCode.toLowerCase()] || langCode;
    }

    /**
     * 执行翻译
     */
    async translate(
        text: string,
        from: string,
        to: string,
        route: ITranslationRoute,
    ): Promise<string | null> {
        try {
            const salt = Date.now().toString();
            const sign = this.generateSign(route.apiKey, text, salt, route.apiSecret || '');

            const response = await axios.get<BaiduTranslationResult>(
                'https://fanyi-api.baidu.com/api/trans/vip/translate',
                {
                    params: {
                        q: text,
                        from,
                        to,
                        appid: route.apiKey,
                        salt,
                        sign,
                    },
                },
            );

            if (response.data.trans_result && response.data.trans_result.length > 0) {
                return response.data.trans_result[0].dst;
            }

            return null;
        } catch (error) {
            return null;
        }
    }

    /**
     * 执行图片翻译
     */
    async translateImage(
        imageBase64: string,
        from: string,
        to: string,
        route: ITranslationRoute,
    ): Promise<string | null> {
        try {
            console.log('开始百度图片翻译，参数:', {
                from,
                to,
                routeId: route.id,
                imageSize: imageBase64.length,
                apiKey: route.apiKey,
            });

            // 获取access_token
            const accessToken = await this.getAccessToken(route.apiKey, route.apiSecret || '');
            if (!accessToken) {
                console.error('获取百度access_token失败');
                return null;
            }

            // 转换语言代码
            const sourceLanguage = this.convertLanguageCodeForImage(from);
            const targetLanguage = this.convertLanguageCodeForImage(to);

            console.log('语言转换:', { from, to, sourceLanguage, targetLanguage });

            // 准备图片数据
            let base64Data = imageBase64;
            if (base64Data.includes(',')) {
                base64Data = base64Data.split(',')[1];
            }

            // 将base64转换为Buffer
            const imageBuffer = Buffer.from(base64Data, 'base64');

            // 创建FormData
            const formData = new FormData();
            // 将Buffer转换为Blob
            const imageBlob = new Blob([imageBuffer], { type: 'image/jpeg' });
            formData.append('image', imageBlob, 'image.jpg');
            formData.append('from', sourceLanguage);
            formData.append('to', targetLanguage);
            formData.append('v', '3');
            formData.append('paste', '1'); // 返回整图贴合

            console.log('准备调用百度图片翻译API...');

            const response = await axios.post<BaiduImageTranslationResult>(
                `https://aip.baidubce.com/file/2.0/mt/pictrans/v1?access_token=${accessToken}`,
                formData,
                {
                    headers: {
                    },
                    timeout: 60000, // 60秒超时
                },
            );

            console.log('百度图片翻译API调用完成');
            console.log('百度API响应:', {
                error_code: response.data.error_code,
                error_msg: response.data.error_msg,
                hasData: !!response.data.data,
                hasPasteImg: !!response.data.data?.pasteImg,
            });

            if (response.data.error_code === '0' && response.data.data) {
                // 如果有贴合图片，返回图片URL或base64
                if (response.data.data.pasteImg) {
                    return `data:image/jpeg;base64,${response.data.data.pasteImg}`;
                }

                // 否则返回文本翻译结果
                if (response.data.data.sumDst) {
                    return response.data.data.sumDst;
                }
            }

            console.error('百度图片翻译失败:', response.data);
            return null;
        } catch (error: any) {
            console.error('百度图片翻译失败:', error);
            if (error.response) {
                console.error('响应错误:', error.response.data);
            }
            return null;
        }
    }

    /**
     * 获取百度access_token
     */
    private async getAccessToken(apiKey: string, secretKey: string): Promise<string | null> {
        try {
            const response = await axios.post('https://aip.baidubce.com/oauth/2.0/token', null, {
                params: {
                    grant_type: 'client_credentials',
                    client_id: apiKey,
                    client_secret: secretKey,
                },
            });

            if (response.data.access_token) {
                return response.data.access_token;
            }

            return null;
        } catch (error) {
            return null;
        }
    }

    /**
     * 智能翻译（失败时自动切换线路）
     */
    async smartTranslate(
        text: string,
        from: string,
        to: string,
    ): Promise<[string | undefined, string | undefined]> {
        const routes = await this.getAvailableBaiduRoutes();

        if (routes.length === 0) {
            return [, '没有可用的翻译线路'];
        }

        // 尝试每条线路，直到成功或全部失败
        for (const route of routes) {
            const result = await this.translate(text, from, to, route);
            if (result !== null) {
                return [result, undefined];
            }
        }
        return [, '翻译失败'];
    }

    /**
     * 智能图片翻译（失败时自动切换线路）
     */
    async smartTranslateImage(
        imageBase64: string,
        from: string,
        to: string,
    ): Promise<[string | undefined, string | undefined]> {
        const routes = await this.getAvailableBaiduImageRoutes();

        if (routes.length === 0) {
            return [, '没有可用的翻译线路'];
        }

        // 尝试每条线路，直到成功或全部失败
        for (const route of routes) {
            const result = await this.translateImage(imageBase64, from, to, route);
            if (result !== null) {
                return [result, undefined];
            }
        }

        return [, '翻译失败'];
    }
}

export default new BaiduTranslationService()
