/// <reference types="vite-plugin-electron/electron-env" />

declare namespace NodeJS {
  interface ProcessEnv {
    VSCODE_DEBUG?: 'true'
    DIST_ELECTRON: string
    DIST: string
    /** /dist/ or /public/ */
    VITE_PUBLIC: string
  }
}

// 定义Bridge接口
declare interface IBridge {
  getUserId(): Promise<string | null>;
  getUserAvatar(): Promise<string | null>;
  getUserNickName(): Promise<string | null>;
  getUnreadCount(): Promise<number>;
  getLoginState(): Promise<boolean>;
  messageObserver: MutationObserver | null
}