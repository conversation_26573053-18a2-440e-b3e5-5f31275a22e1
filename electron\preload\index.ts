import { ipc<PERSON><PERSON><PERSON>, contextBridge } from 'electron'
import { TelegramBridge } from './telegram';
import { WhatsAppBridge } from './whatsApp';
import { PlatformKeys } from '../main/enums';

// 从 process.argv 中提取 additionalArguments
const args = process.argv;
const platform = args.find(arg => arg.startsWith('@platform='))?.split('=')[1];
switch (platform) {
  case PlatformKeys.WhatsApp:
    // contextIsolation = true 时无法使用 contextBridge
    // contextBridge.exposeInMainWorld('JSBridge', new WhatsAppBridge());
    (window as any).JSBridge = new WhatsAppBridge();
    break;
  case PlatformKeys.Telegram:
    contextBridge.exposeInMainWorld('JSBridge', new TelegramBridge());
    break;
  default:
    // --------- Expose some API to the Renderer process ---------
    contextBridge.exposeInMainWorld('ipcRenderer', {
      on(...args: Parameters<typeof ipcRenderer.on>) {
        const [channel, listener] = args
        return ipcRenderer.on(channel, (event, ...args) => listener(event, ...args))
      },
      off(...args: Parameters<typeof ipcRenderer.off>) {
        const [channel, ...omit] = args
        return ipcRenderer.off(channel, ...omit)
      },
      send(...args: Parameters<typeof ipcRenderer.send>) {
        const [channel, ...omit] = args
        return ipcRenderer.send(channel, ...omit)
      },
      invoke(...args: Parameters<typeof ipcRenderer.invoke>) {
        const [channel, ...omit] = args
        return ipcRenderer.invoke(channel, ...omit)
      },
    })
    break;
}