import { PermissionKey, VendorType } from "../../enums";
import { ITranslateImageParams, ITranslateTextParams } from "../../interface";
import youdaoTranslateService from "./youdao";
import baiduTranslateService from "./baidu";
import alibabaTranslateService from "./alibaba";
import { checkPermission } from "../../modules/permission";

/**
 * 文本翻译
 * @param option 
 * @returns 
 */
export async function translateText(option: ITranslateTextParams): Promise<[string | undefined, string | undefined]> {
    if (!option.from || !option.to || !option.vendor) {
        return [, "请检查翻译配置"]
    }
    if (!checkPermission(PermissionKey.TranslationText)) {
        return [, "请开通会员使用此功能"]
    }
    switch (option.vendor) {
        case VendorType.YOUDAO:
            return await youdaoTranslateService.smartTranslate(option.text, option.from, option.to)
        case VendorType.BAIDU:
            return await baiduTranslateService.smartTranslate(option.text, option.from, option.to)
        default:
            return [, "翻译线路不存在"]
    }
}

/**
 * 图片翻译
 * @param option 
 * @returns 
 */
export async function translateImage(option: ITranslateImageParams): Promise<[string | undefined, string | undefined]> {
    if (!option.from || !option.to || !option.vendor) {
        return [, "请检查翻译配置"]
    }
    if (!checkPermission(PermissionKey.TranslationText)) {
        return [, "请开通会员使用此功能"]
    }
    switch (option.vendor) {
        case VendorType.YOUDAO:
            return await youdaoTranslateService.smartTranslateImage(option.imageBase64, option.from, option.to)
        case VendorType.BAIDU:
            return await baiduTranslateService.smartTranslateImage(option.imageBase64, option.from, option.to)
        case VendorType.ALIBABA:
            return await alibabaTranslateService.smartTranslateImage(option.imageBase64, option.from, option.to)
        default:
            return [, "翻译线路不存在"]
    }
}