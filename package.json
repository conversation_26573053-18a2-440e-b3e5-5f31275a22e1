{"name": "<PERSON>_Whale_AI", "version": "1.0.3", "main": "dist-electron/main/index.js", "description": "Really simple Electron + Vue + Vite boilerplate.", "author": "chen <<EMAIL>>", "license": "MIT", "private": true, "keywords": ["electron", "rollup", "vite", "vue3", "vue"], "debug": {"env": {"VITE_DEV_SERVER_URL": "http://127.0.0.1:3344/"}}, "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build && electron-builder", "build:mac": "vue-tsc --noEmit && vite build && electron-builder --mac", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "tailwind:build": "npx tailwindcss -i ./src/style.css -o ./src/tailwind.css --watch"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "@vicons/antd": "^0.12.0", "@vicons/carbon": "^0.12.0", "@vicons/fa": "^0.12.0", "@vicons/fluent": "^0.12.0", "@vicons/ionicons4": "^0.12.0", "@vicons/ionicons5": "^0.12.0", "@vicons/material": "^0.12.0", "@vicons/tabler": "^0.12.0", "@vitejs/plugin-vue": "^5.0.4", "autoprefixer": "^10.4.20", "electron": "^29.1.1", "electron-builder": "^24.13.3", "eslint": "^9.23.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-vue": "^10.0.0", "postcss": "^8.4.47", "prettier": "^3.5.3", "sass": "^1.77.4", "tailwindcss": "^3.4.14", "typescript": "^5.4.2", "unplugin-auto-import": "^0.12.1", "unplugin-vue-components": "^0.22.12", "vfonts": "^0.0.3", "vite": "^5.1.5", "vite-plugin-electron": "^0.28.4", "vite-plugin-electron-renderer": "^0.14.5", "vue": "^3.4.21", "vue-tsc": "^2.0.6"}, "dependencies": {"@alicloud/alimt20181012": "^1.4.1", "@alicloud/openapi-client": "^0.4.15", "@alicloud/tea-util": "^1.4.10", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.2.6", "electron-store": "^10.0.1", "electron-updater": "^6.2.1", "element-plus": "^2.9.7", "form-data": "^4.0.4", "js-cookie": "^3.0.5", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "save": "^2.9.0", "vite-plugin-svg-icons": "^2.0.1", "vue-router": "^4.1.6", "winston": "^3.14.1"}}