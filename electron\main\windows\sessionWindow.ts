import { BrowserWindow, Rectangle, session, WebContents } from "electron";
import { BaseWindowsManager } from "./baseWindow";
import coreWindowsManager from './coreWindow'
import {
    chatWindowCreatedEmit,
    chatWindowShowEmit,
    chatWindowHideEmit,
    chatWindowClosedEmit,
    chatWindowResizedEmit,
    chatWindowReadyedEmit,
    chatWindowLoadFailEmit,
    chatWindowStartLoadEmit,
    platformAccountEmit
} from '../modules/emitter'
import { IAccountInfo, SessionWindowsManagerOptions } from "../interface";
import { storeManager } from '../modules/store';
import { PRELOAD } from "../config/config";
import { PlatformKeys } from "../enums";

class SessionWindowsManager extends BaseWindowsManager {
    constructor(params: SessionWindowsManagerOptions) {
        super();
        this.init(params)
    }

    protected win: BrowserWindow;
    public webContents: WebContents
    public winId: string;
    public winUrl: string;
    public isShow: boolean = false;
    public isLoading: boolean = false;
    public isLoaded: boolean = false;
    public isLoadFail: boolean = false;
    public accountInfo: IAccountInfo = {
        isLogined: false,
        unreadCount: 0,
        avatar: '',
        accountId: '',
        nickname: ''
    }

    init({ id, url, platform, bounds, proxyConfig }: SessionWindowsManagerOptions) {
        const bgWindow = coreWindowsManager.getWindow('background');
        if (bgWindow) {
            // 生成唯一标识
            const uniqueId = Date.now() + Math.random().toString(36).substring(2)
            // 创建独立session
            const savePartition = storeManager.getPartition(this.winId)
            const partition = savePartition || ('persist:' + uniqueId)
            if (!savePartition) {
                storeManager.setPartition(this.winId, partition);
            }
            const ses = session.fromPartition(partition)
            const fingerprintConfig = storeManager.getFingerprintConfig(id);

            // 如果之前保存过 User-Agent，使用相同的
            const savedUserAgent = storeManager.getUserAgent(this.winId);
            const defaultUserAgent = savedUserAgent || `Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36`
            const userAgent = fingerprintConfig?.enabled && fingerprintConfig?.userAgent ? fingerprintConfig.userAgent : defaultUserAgent;
            // 保存 User-Agent 以便下次使用
            if (!savedUserAgent) {
                storeManager.setUserAgent(this.winId, userAgent);
            }
            // 修改指纹信息
            ses.setUserAgent(userAgent)
            const options: Electron.BrowserWindowConstructorOptions = {
                show: false,
                parent: bgWindow,
                frame: false,//是否无边框
                transparent: true,//透明窗口
                backgroundColor: '#00000000',//窗口底色为透明色
                resizable: false,
                webPreferences: {
                    v8CacheOptions: 'none',
                    session: ses,
                    preload: PRELOAD,
                    webgl: false,
                    plugins: false,
                    contextIsolation: platform !== PlatformKeys.WhatsApp,
                    additionalArguments: [
                        `@chatId=${id}`,
                        `@platform=${platform}`
                    ]
                }
            }
            this.winId = id;
            this.winUrl = url;
            this.isLoading = true;
            this.isLoaded = false;
            this.isLoadFail = false;
            chatWindowStartLoadEmit(this.winId)
            this.win = this.createWindow(id, url, options, proxyConfig);
            this.webContents = this.win.webContents;

            // 恢复localStorage
            this.restoreLocalStorage();

            if (import.meta.env.DEV) {
                this.webContents.openDevTools({ mode: 'detach' })
            }
            this.webContents?.setUserAgent(userAgent);
            this.setBounds(bounds);
            this.setEventListener(this.win)
            chatWindowCreatedEmit(id);

            // 恢复cookies
            this.restoreCookies(ses);
        }
    }

    private async restoreCookies(ses: Electron.Session) {
        const cookies = storeManager.getCookies(this.winId);
        if (cookies) {
            for (const cookie of cookies) {
                try {
                    await ses.cookies.set({
                        url: this.winUrl,
                        name: cookie.name,
                        value: cookie.value,
                        path: cookie.path || '/',
                        secure: cookie.secure,
                        httpOnly: cookie.httpOnly,
                        expirationDate: cookie.expirationDate
                    });
                } catch (error) {
                    console.error('Failed to restore cookie:', error, cookie);
                }
            }
        }
    }

    private async restoreLocalStorage() {
        const localStorageData = storeManager.getLocalStorage(this.winId);
        if (localStorageData) {
            // 等待页面加载完成后再注入数据，不要重新加载页面
            this.webContents.once('dom-ready', async () => {
                try {
                    await this.webContents.executeJavaScript(`
                        (function() {
                            const data = ${JSON.stringify(localStorageData)};
                            for (const [key, value] of Object.entries(data)) {
                                localStorage.setItem(key, value);
                            }
                            location.reload()
                        })();
                    `);
                } catch (error) {
                    console.error('Failed to restore localStorage:', error);
                }
            });
        }
    }

    protected setEventListener(win: BrowserWindow) {
        win.on('show', () => {
            this.isShow = true;
            chatWindowShowEmit(this.winId)
        })
        win.on('hide', () => {
            this.isShow = false;
            chatWindowHideEmit(this.winId)
        })
        win.on('closed', () => {
            chatWindowClosedEmit(this.winId)
        })
        win.on('resized', (e, newBounds: Rectangle) => {
            chatWindowResizedEmit(newBounds)
        })
        win.webContents.on('dom-ready', () => {
            this.isLoaded = true;
            chatWindowReadyedEmit(this.winId)
        })
        win.webContents.on('did-finish-load', () => {
            this.isLoading = false;
            win.webContents.insertCSS(`
                [data-text-enable-translate="true"] .translate-button-container {
                    display: flex !important;
                }
                [data-text-enable-translate="false"] .translate-button-container {
                    display: none !important;
                }
                [data-text-enable-translate="true"] .translated-content {
                    display: block !important;
                }
                [data-text-enable-translate="false"] .translated-content {
                    display: none !important;
                }
            `)
        })
        win.webContents.on('did-fail-load', () => {
            this.isLoading = false;
            this.isLoaded = false;
            this.isLoadFail = true;
            chatWindowLoadFailEmit(this.winId)
        })
        win.webContents.on('ipc-message', async (event, channel, data: any) => {
            switch (channel) {
                case 'account-login':
                    this.accountInfo.isLogined = data.state;
                    platformAccountEmit(this.winId, await this.getAccountInfo())
                    break;
                case 'localStorage-changed':
                    // 获取所有localStorage数据
                    const localStorageData = await win.webContents.executeJavaScript(`
                        (function() {
                            const data = {};
                            for (let i = 0; i < localStorage.length; i++) {
                                const key = localStorage.key(i);
                                data[key] = localStorage.getItem(key);
                            }
                            return data;
                        })();
                    `);
                    // 保存localStorage数据
                    storeManager.setLocalStorage(this.winId, localStorageData);
                    break;
            }
        })

        // 监听cookies变化
        win.webContents.session.cookies.on('changed', async (event, cookie, cause, removed) => {
            if (!removed) {
                // 获取所有cookies
                const cookies = await win.webContents.session.cookies.get({});
                // 保存cookies
                storeManager.setCookies(this.winId, cookies);
            }
        });
    }

    public reload() {
        this.isLoading = true;
        this.isLoaded = false;
        this.isLoadFail = false;
        chatWindowStartLoadEmit(this.winId)
        this.win.webContents.reload();
    }

    setBounds(bounds: Rectangle) {
        const mainWindow = coreWindowsManager.getWindow('main');
        const mainBounds = mainWindow.getBounds();
        const childBounds = {
            width: bounds.width,
            height: bounds.height,
            x: bounds.x + mainBounds.x,
            y: bounds.y + mainBounds.y,
        }
        this.win!.setBounds(childBounds, false);
    }

    show() {
        if (!this.win!.isDestroyed()) {
            this.win!.show();
        };
    }

    hide() {
        if (!this.win!.isDestroyed()) {
            this.win!.hide();
        }
    }

    destroy() {
        this.win!.destroy();
    }

    isVisible() {
        return this.win!.isVisible();
    }

    async getExecuteJavaScriptRes(jsString: string) {
        return await this.win?.webContents
            .executeJavaScript(jsString)
            .then((res) => res)
            .catch(() => '');
    }

    /**
     * 获取账号信息
     * @returns
     */
    async getAccountInfo(): Promise<IAccountInfo> {
        this.accountInfo.accountId = await this.getUserId();
        this.accountInfo.avatar = await this.getUserAvatar();
        this.accountInfo.nickname = await this.getUserNickName();
        this.accountInfo.unreadCount = await this.getUnreadCount();
        this.accountInfo.isLogined = await this.getLoginState();
        return this.accountInfo;
    }

    /**
     * 获取账号id
     * @returns
     */
    async getUserId(): Promise<string> {
        return await this.getExecuteJavaScriptRes(`window.JSBridge.getUserId()`)
    }

    /**
     * 获取账号头像
     * @returns
     */
    async getUserAvatar(): Promise<string> {
        return await this.getExecuteJavaScriptRes(`window.JSBridge.getUserAvatar()`)
    }

    /**
     * 获取账号昵称
     * @returns
     */
    async getUserNickName(): Promise<string> {
        return await this.getExecuteJavaScriptRes(`window.JSBridge.getUserNickName()`)
    }

    /**
     * 获取未读消息数
     * @returns
     */
    async getUnreadCount(): Promise<number> {
        return await this.getExecuteJavaScriptRes(`window.JSBridge.getUnreadCount()`)
    }

    /**
     * 获取登录状态
     * @returns
     */
    async getLoginState(): Promise<boolean> {
        return await this.getExecuteJavaScriptRes(`window.JSBridge.getLoginState()`)
    }

}

export { SessionWindowsManager }