<template>
    <div class="flex justify-end items-center z-[9999999] rounded-tr-lg overflow-hidden w-full h-[38px] bg-[#edeff2]"
        v-if="btnState.hideTitle !== false">
        <div class="h-full flex-grow -webkit-app-region-drag flex items-center pl-[15px] text-xs font-bold">
            <img class="h-4 w-4 rounded align-bottom" src="@/assets/images/logo-light.png"></img>
            <div class="ml-1">巨鲸跨境助手</div>
        </div>
        <div class="w-[40px] h-full flex justify-center items-center cursor-pointer hover:bg-white/20"
            v-if="btnState.minimizable" @click.stop="winMinimize()">
            <el-icon size="18" color="#000">
                <Subtract16Filled></Subtract16Filled>
            </el-icon>
        </div>
        <div class="w-[40px] h-full flex justify-center items-center cursor-pointer hover:bg-white/20"
            v-if="btnState.fullscreenable" @click.stop="winMaximize()">
            <el-icon size="14" color="#000">
                <BorderOutside24Regular></BorderOutside24Regular>
            </el-icon>
        </div>
        <div class="w-[40px] h-full flex justify-center items-center cursor-pointer hover:bg-white/20"
            v-if="btnState.closeable" @click.stop="winClose()">
            <el-icon size="18" color="#000">
                <Dismiss20Regular></Dismiss20Regular>
            </el-icon>
        </div>
    </div>
</template>
<script setup lang="ts">
import { BorderOutside24Regular, Subtract16Filled, Dismiss20Regular } from '@vicons/fluent'
import { useRoute } from 'vue-router'
import { computed } from 'vue'
import { winMinimize, winClose, winMaximize } from '../../utils/windows';
const route = useRoute();

const btnState = computed(() => {
    const { minimizable, fullscreenable, closeable, hideTitle } = route?.meta || {};
    return { minimizable: minimizable, fullscreenable: fullscreenable, closeable: closeable, hideTitle: hideTitle };
})
</script>

<style>
.-webkit-app-region-drag {
    -webkit-app-region: drag;
}
</style>