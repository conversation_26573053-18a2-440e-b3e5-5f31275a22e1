export enum IEventType {
    QuitApp = 'quitApp',
    ToLoginWindow = 'toLoginWindow',
    ToMainWindow = 'toMainWindow',
    ToUpdateWindow = 'toUpdateWindow',
    CreateChatWindow = 'createChatWindow',
    SetChatWindowBounds = 'setChatWindowBounds',
    WinMinimize = 'winMinimize',
    WinClose = 'winClose',
    WinMaximize = 'winMaximize',
    ShowChatWindow = 'showChatWindow',
    HideChatWindow = 'hideChatWindow',
    CloseChatWindow = 'closeChatWindow',
    GetChatWindowInfo = 'getChatWindowInfo',
    GetChatWindowInfoList = 'getChatWindowInfoList',
    ReloadChatWindow = 'reloadChatWindow',
    SetTranslateConfig = 'set-translate-config',
    GetTranslateConfig = 'get-translate-config',
    SetTranslateRoutes = 'set-translate-routes',
    GetTranslateRoutes = 'get-translate-routes',
    SetProxyConfig = 'set-proxy-config',
    GetProxyConfig = 'get-proxy-config',
    SetFingerprintConfig = 'set-fingerprint-config',
    GetFingerprintConfig = 'get-fingerprint-config',
    TranslateText = 'translate-text',
    TranslateImage = 'translate-image',
    GetToken = 'get-token',
    SetToken = 'set-token',
    RemoveToken = 'remove-token',
    GetPermissions = 'get-permissions',
    SetPermissions = 'set-permissions',
    RemovePermissions = 'remove-permissions',
    ShowToast = 'show-toast'
}

export enum IEmitType {
    ChatWindowEmit = 'chatWindowEmit',
    OnChatWindowCreated = 'onChatWindowCreated',
    OnChatWindowShow = 'onChatWindowShow',
    OnChatWindowHide = 'onChatWindowHide',
    OnChatWindowClosed = 'onChatWindowClosed',
    OnChatWindowResized = 'onChatWindowResized',
    OnChatWindowReadyed = 'onChatWindowReadyed',
    OnChatWindowLoadFail = 'onChatWindowLoadFail',
    OnChatWindowStartLoading = 'onChatWindowStartLoading',
    OnPlatformAccountChange = 'onPlatformAccountChange',
    ShowToast = 'showToast'
}

export enum ICoreWindowId {
    Main = 'main',
    Login = 'login',
    Update = 'update',
    Launch = 'launch'
}

export enum PlatformKeys {
    WhatsApp = 'whatsapp',
    Telegram = 'telegram',
    LINE = 'line',
}

export enum TranslationType {
    TEXT = 'text',
    AUDIO = 'audio',
    VIDEO = 'video',
    IMAGE = 'image',
    DOCUMENT = 'document',
}

export enum VendorType {
    // GOOGLE = 'google',
    BAIDU = 'baidu',
    YOUDAO = 'youdao',
    ALIBABA = 'alibaba',
    // DEEPL = 'deepl'
}

export enum PermissionKey {
    TranslationText = 'translation:text',
    TranslationImage = 'translation:image',
    TranslationAudio = 'translation:audio',
}