import { ipc<PERSON><PERSON><PERSON> } from 'electron'
import { domReady, onLocalStorageItemChange } from './tools';
import { CommonBridge } from './common'

// 声明 WPP 类型以解决 TypeScript 错误
declare global {
    interface Window {
        WPP: any;
    }
}

// --------- Expose some API to the Renderer process ---------
// 实现Bridge类
export class WhatsAppBridge extends CommonBridge implements IBridge {
    constructor() {
        super();
        // 确保方法绑定到实例
        this.getUserId = this.getUserId.bind(this);
        this.getUserAvatar = this.getUserAvatar.bind(this);
        this.getUserNickName = this.getUserNickName.bind(this);
        this.getUnreadCount = this.getUnreadCount.bind(this);
        this.getLoginState = this.getLoginState.bind(this);
        domReady().then(async () => {
            ipcRenderer.send('account-login', { state: false });
            ipcRenderer.on('translate-config-changed', () => {
                this.setTranslateState();
            });
            this.setTranslateState();
            this.setupTranslateInput();
            this.listenChatMessageChange();
        });
        ipcRenderer.once('wppconnect-injected', () => {
            this.listenAuthChange();
        })
    }

    messageObserver = null;

    private async setupTranslateInput() {
        this.initTranslateInput('footer .lexical-rich-text-input [contenteditable="true"]');
    }

    private listenAuthChange() {
        try {
            window.WPP.webpack.onReady(() => {
                console.log('onReady')
                // 监听连接状态变化
                window.WPP.ev.on('conn.main_ready', () => {
                    console.log('WhatsApp is ready');
                    ipcRenderer.send('localStorage-changed');
                    ipcRenderer.send('account-login', { state: true });
                });

                window.WPP.ev.on('conn.authenticated', () => {
                    console.log('User authenticated');
                    ipcRenderer.send('localStorage-changed');
                    ipcRenderer.send('account-login', { state: true });
                });

                window.WPP.ev.on('conn.logout', () => {
                    console.log('User logged out');
                    ipcRenderer.send('localStorage-changed');
                    ipcRenderer.send('account-login', { state: false });
                });

                window.WPP.ev.on('conn.online', async (state) => {
                    console.log('Connection state:', state);
                    const isLoggedIn = state === 'CONNECTED' && await this.getLoginState();
                    ipcRenderer.send('account-login', { state: isLoggedIn });
                });

                // 监听二维码状态
                window.WPP.ev.on('qr', (qr) => {
                    console.log('QR Code generated:', qr);
                    ipcRenderer.send('account-login', { state: false });
                });
            });
        } catch (e) {
            console.log('listenAuthChange error:', e);
        }
    }

    private listenChatMessageChange() {
        this.messageObserver = this.setupMessageObserver('#app', () => {
            this.initTranslateChatItems('.message-in, .message-out', '.copyable-text, .copyable-text-container, [role="button"]');
        });
    }

    public async getUserId() {
        try {
            if (!window.WPP) return null;
            const account = window.WPP.conn.getMyUserId();
            return account?.user || null;
        } catch {
            return null;
        }
    }

    public async getUserAvatar() {
        try {
            if (!window.WPP) return null;
            const picture = await window.WPP.profile.getMyProfilePicture();
            return picture?.imgFull || null;
        } catch {
            return null;
        }
    }

    public async getUserNickName() {
        try {
            if (!window.WPP) return null;
            const nickname = await window.WPP.profile.getMyProfileName();
            return nickname || null;
        } catch {
            return null;
        }
    }

    public async getUnreadCount() {
        try {
            const badges = document.querySelectorAll('span[aria-label*="unread"]');
            return Array.from(badges).reduce((sum, badge) => {
                const text = badge.textContent?.trim() || '0';
                return sum + parseInt(text, 10);
            }, 0);
        } catch {
            return 0;
        }
    }

    public async getLoginState() {
        try {
            if (!window.WPP) return false;
            const isOnline = window.WPP.conn.isOnline();
            return !!isOnline;
        } catch {
            return false;
        }
    }
}

