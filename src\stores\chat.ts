// Pinia Store
import { defineStore } from 'pinia'
import { createChatAccountAPI, IChatAccountsBo, listChatAccountAPI, deleteChatAccountAPI } from '../api/chat'
import { IEventType, PlatformKeys } from '../constants/enum';
import { Rectangle } from 'electron';
import { getChatWindowInfo, getChatWindowInfoList } from '../utils/ipcRenderer';
import { IAccountInfo, IChatWindowStatus } from '../types';

interface IState {
    chatList: IChatAccountsBo[],
    chatAccounts: Record<string, IAccountInfo>,
    selectedChatId: string
};

const defatltChatWindowInfo: IChatWindowStatus = {
    isLoading: false,
    isShow: false,
    isLoaded: false,
    isLoadFail: false,
    isCreated: false,
}

export const useChatStore = defineStore('chat', {
    // 转换为函数
    state: (): IState => ({
        chatList: [],
        chatAccounts: {},
        selectedChatId: ''
    }),
    actions: {
        async loadChatList() {
            const res = await listChatAccountAPI();
            this.chatList = res.data || [];
            this.refreshChatList();
            this.showChat(this.selectedChatId || this.chatList[0]?.id || '')
        },
        async refreshChatList() {
            const chatWindowInfoList = await getChatWindowInfoList();
            this.chatList = this.chatList.map(item => {
                const chatWindowInfo = chatWindowInfoList.find((i: { winId: string; }) => i.winId === item.id) || defatltChatWindowInfo;
                const accountInfo = this.chatAccounts[item.id] || {};
                const injectInfo = {
                    avatar: accountInfo.avatar || chatWindowInfo.avatar,
                    accountId: accountInfo.accountId || chatWindowInfo.accountId,
                    nickname: accountInfo.nickname || chatWindowInfo.nickname,
                    ...chatWindowInfo,
                }
                return {
                    ...item,
                    ...injectInfo
                }
            });
        },
        async createChatAccount(platform: PlatformKeys) {
            const data: any = await createChatAccountAPI(platform)
            await this.loadChatList();
            this.showChat(data.data.id)
        },
        async createChatWindow(id: string, platform: string, bounds: Rectangle) {
            await window.ipcRenderer?.invoke(IEventType.CreateChatWindow, id, platform, bounds)
        },
        async showChat(id: string) {
            this.setSelectedChatId(id)
            if (id) {
                await window.ipcRenderer?.invoke(IEventType.ShowChatWindow, id);
                this.refreshChatList()
            }
        },
        async closeChat(id: string) {
            await window.ipcRenderer?.invoke(IEventType.CloseChatWindow, id);
            this.refreshChatList()
        },
        async deleteChat(id: string) {
            await this.closeChat(id)
            await deleteChatAccountAPI(id);
            await this.loadChatList();
            if (id === this.selectedChatId) {
                this.showChat(this.chatList[0]?.id || '')
            }
        },
        async reloadChat(id: string) {
            await window.ipcRenderer?.invoke(IEventType.ReloadChatWindow, id);
        },
        setSelectedChatId(id: string) {
            this.selectedChatId = id;
        },
        setChatWindowState(id: string, key: keyof IChatWindowStatus, value: boolean) {
            for (const win of this.chatList) {
                if (win.id === id) {
                    win[key] = value
                    break
                }
            }
        },
        setChatWindowAccountInfo(id: string, accountInfo: IAccountInfo) {
            this.chatAccounts[id] = accountInfo;
            this.refreshChatList();
        }
    },
    persist: {
        key: 'chat-store',
        storage: localStorage,
        paths: ['selectedChatId', 'chatAccounts']
    },
})
