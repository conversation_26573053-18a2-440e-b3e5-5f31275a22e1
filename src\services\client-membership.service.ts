import { FindOptionsWhere, Repository, <PERSON>Than } from 'typeorm';
import { AppDataSource } from '../config/database';
import { BaseService } from './base.service';
import { ClientMembership } from '../entities/ClientMembership';
import { MembershipProduct } from '../entities/MembershipProduct';
import { Client } from '../entities/Client';
import { addDays } from '../utils/date.util';

/**
 * 用户会员服务
 */
export class ClientMembershipService extends BaseService<ClientMembership> {
  private membershipProductRepository: Repository<MembershipProduct>;

  constructor() {
    super(AppDataSource.getRepository(ClientMembership));
    this.membershipProductRepository = AppDataSource.getRepository(MembershipProduct);
  }

  /**
   * 添加会员时长
   * @param clientId 用户ID
   * @param productId 会员商品ID
   * @param duration 会员时长(天)
   */
  async addMembership(
    clientId: string,
    productId: string,
    duration: number,
  ): Promise<ClientMembership> {
    // 获取会员产品
    const product = await this.membershipProductRepository.findOne({
      where: { id: productId },
    });

    if (!product) {
      throw new Error('会员商品不存在');
    }

    // 查询用户当前的会员状态
    const currentMembership = await this.getCurrentMembership(clientId);

    let startTime: Date;
    let endTime: Date;

    if (currentMembership && currentMembership.status === 'active') {
      // 如果用户已有会员且未过期，续期
      startTime = currentMembership.startTime;
      endTime = addDays(currentMembership.endTime, duration);

      // 更新原会员记录
      await this.update(currentMembership.id, { endTime });
      return currentMembership;
    } else {
      // 如果用户没有会员或已过期，创建新会员
      startTime = new Date();
      endTime = addDays(startTime, duration);

      // 创建新会员记录
      return this.create({
        client: { id: clientId } as Client,
        membershipProduct: product,
        startTime,
        endTime,
        status: 'active',
      });
    }
  }

  /**
   * 获取用户当前会员状态
   */
  async getCurrentMembership(clientId: string): Promise<ClientMembership | null> {
    const now = new Date();

    // 查询用户当前的会员状态
    const membership = await this.repository.findOne({
      where: {
        client: { id: clientId } as Client,
        status: 'active',
        endTime: MoreThan(now),
      } as FindOptionsWhere<ClientMembership>,
      relations: [
        'membershipProduct',
        'membershipProduct.permissions',
        'membershipProduct.permissions.permission',
      ],
      order: { endTime: 'DESC' },
    });

    return membership;
  }

  /**
   * 检查用户是否过期
   * 会检查数据库中的会员状态，如果已过期会自动更新状态
   */
  async checkAndUpdateMembershipStatus(clientId: string): Promise<boolean> {
    const now = new Date();

    // 查询用户当前的会员状态
    const memberships = await this.repository.find({
      where: {
        client: { id: clientId } as Client,
        status: 'active',
      } as FindOptionsWhere<ClientMembership>,
    });

    // 检查是否有过期会员，有则更新状态
    const expiredMemberships = memberships.filter((m) => m.endTime < now);
    for (const membership of expiredMemberships) {
      await this.update(membership.id, { status: 'expired' });
    }

    // 返回用户是否还有有效会员
    const validMemberships = memberships.filter((m) => m.endTime >= now);
    return validMemberships.length > 0;
  }

  /**
   * 检查用户是否有某个权限
   */
  async checkClientPermission(clientId: string, permissionKey: string): Promise<boolean> {
    // 先检查会员状态
    const hasMembership = await this.checkAndUpdateMembershipStatus(clientId);
    if (!hasMembership) {
      return false;
    }

    // 查询用户当前有效的会员
    const membership = await this.repository.findOne({
      where: {
        client: { id: clientId } as Client,
        status: 'active',
        endTime: MoreThan(new Date()),
      } as FindOptionsWhere<ClientMembership>,
      relations: [
        'membershipProduct',
        'membershipProduct.permissions',
        'membershipProduct.permissions.permission',
      ],
    });

    if (!membership) {
      return false;
    }

    // 检查会员是否有该权限
    const hasPermission = membership.membershipProduct.permissions.some(
      (mp) => mp.permission.key === permissionKey && mp.permission.isActive,
    );

    return hasPermission;
  }
}
